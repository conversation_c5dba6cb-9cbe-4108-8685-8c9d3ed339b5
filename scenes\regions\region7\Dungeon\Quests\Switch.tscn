[gd_scene load_steps=7 format=3 uid="uid://but0dxwcbtart"]

[ext_resource type="Texture2D" uid="uid://dluyp1a4yxngd" path="res://resources/solaria/crypt/switch_black.png" id="1_t31ua"]

[sub_resource type="Animation" id="Animation_t31ua"]
resource_name = "Open"
length = 0.3
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SwitchSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [0, 1, 2]
}

[sub_resource type="Animation" id="Animation_ehehk"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SwitchSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="Animation" id="Animation_7skqi"]
resource_name = "Close"
length = 0.3
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SwitchSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [2, 1, 0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_jroqp"]
_data = {
&"Close": SubResource("Animation_7skqi"),
&"Open": SubResource("Animation_t31ua"),
&"RESET": SubResource("Animation_ehehk")
}

[sub_resource type="CircleShape2D" id="CircleShape2D_ehehk"]

[node name="Switch" type="Node2D"]

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_jroqp")
}

[node name="SwitchSprite" type="Sprite2D" parent="."]
texture = ExtResource("1_t31ua")
hframes = 3

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
position = Vector2(0, 7)
shape = SubResource("CircleShape2D_ehehk")

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(-7, 13, 7, 13, 7, 4, -7, 4)
