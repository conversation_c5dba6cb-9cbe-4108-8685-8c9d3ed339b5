using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class DungeonManager : Node2D
{
	[Export] public PackedScene DungeonVaseScene { get; set; }

	private Dictionary<Vector2I, Node2D> _activeObjects = new();
	private List<DungeonObjectData> _initialObjectsConfig = new();

	public override void _Ready()
	{
		// Define initial objects configuration
		SetupInitialObjectsConfig();

		// Load existing objects or initialize for first time
		CallDeferred(nameof(LoadOrInitializeObjects));
	}

	private void SetupInitialObjectsConfig()
	{
		// Define the initial dungeon objects and their positions
		_initialObjectsConfig = new List<DungeonObjectData>
		{
			new DungeonObjectData
			{
				TilePosition = new Vector2I(4,-8),
				ObjectType = DungeonObjectType.DungeonVase,
				MaxHealth = 10,
				CurrentHealth = 10,
				IsDestroyed = false
			},
			new DungeonObjectData
			{
				TilePosition = new Vector2I(5,-8),
				ObjectType = DungeonObjectType.DungeonVase,
				MaxHealth = 10,
				CurrentHealth = 10,
				IsDestroyed = false
			}
		};
	}

	private void LoadOrInitializeObjects()
	{
		if (!GameSaveData.Instance.DungeonData.IsInitialized)
		{
			// First time initialization
			InitializeDungeonObjects();
		}
		else
		{
			// Load existing objects from save data
			LoadExistingObjects();
		}
	}

	private void InitializeDungeonObjects()
	{
		GD.Print("DungeonManager: Initializing dungeon objects for first time");

		// Copy initial config to save data
		GameSaveData.Instance.DungeonData.Objects = new List<DungeonObjectData>(_initialObjectsConfig);
		GameSaveData.Instance.DungeonData.IsInitialized = true;

		// Spawn all initial objects
		foreach (var objectData in _initialObjectsConfig)
		{
			SpawnDungeonObject(objectData);
		}

		GD.Print($"DungeonManager: Initialized {_initialObjectsConfig.Count} dungeon objects");
	}

	private void LoadExistingObjects()
	{
		GD.Print("DungeonManager: Loading existing dungeon objects from save data");

		var savedObjects = GameSaveData.Instance.DungeonData.Objects;
		int spawnedCount = 0;

		foreach (var objectData in savedObjects)
		{
			if (!objectData.IsDestroyed)
			{
				SpawnDungeonObject(objectData);
				spawnedCount++;
			}
		}

		GD.Print($"DungeonManager: Loaded {spawnedCount} existing dungeon objects");
	}

	private void SpawnDungeonObject(DungeonObjectData objectData)
	{
		Node2D spawnedObject = null;

		switch (objectData.ObjectType)
		{
			case DungeonObjectType.DungeonVase:
				spawnedObject = SpawnDungeonVase(objectData);
				break;
			default:
				GD.PrintErr($"DungeonManager: Unknown object type {objectData.ObjectType}");
				return;
		}

		if (spawnedObject != null)
		{
			_activeObjects[objectData.TilePosition] = spawnedObject;
		}
	}

	private Node2D SpawnDungeonVase(DungeonObjectData objectData)
	{
		if (DungeonVaseScene == null)
		{
			GD.PrintErr("DungeonManager: DungeonVaseScene not set!");
			return null;
		}

		var vase = DungeonVaseScene.Instantiate<DungeonVase>();
		if (vase == null)
		{
			GD.PrintErr("DungeonManager: Failed to instantiate DungeonVase!");
			return null;
		}

		// Set position at center of tile (16x16 tiles)
		Vector2 worldPosition = new Vector2(
			objectData.TilePosition.X * 16 + 8,
			objectData.TilePosition.Y * 16 + 8
		);

		vase.GlobalPosition = worldPosition;
		vase.Initialize(objectData.TilePosition, objectData.CurrentHealth, objectData.MaxHealth);

		// Connect to destruction signal
		vase.DungeonVaseDestroyed += OnDungeonObjectDestroyed;

		// Add to scene
		GetParent().CallDeferred("add_child", vase);

		GD.Print($"DungeonManager: Spawned DungeonVase at tile {objectData.TilePosition} with {objectData.CurrentHealth}/{objectData.MaxHealth} HP");

		return vase;
	}

	private void OnDungeonObjectDestroyed(Vector2I tilePosition, DungeonObjectType objectType)
	{
		GD.Print($"DungeonManager: Object destroyed at {tilePosition}");

		// Remove from active objects
		if (_activeObjects.ContainsKey(tilePosition))
		{
			_activeObjects.Remove(tilePosition);
		}

		// Update save data - mark as destroyed
		var objectData = GameSaveData.Instance.DungeonData.Objects
			.FirstOrDefault(obj => obj.TilePosition == tilePosition && obj.ObjectType == objectType);

		if (objectData != null)
		{
			objectData.IsDestroyed = true;
			objectData.CurrentHealth = 0;
			GD.Print($"DungeonManager: Marked object at {tilePosition} as destroyed in save data");
		}
	}

	public void UpdateObjectHealth(Vector2I tilePosition, int currentHealth)
	{
		// Update health in save data
		var objectData = GameSaveData.Instance.DungeonData.Objects
			.FirstOrDefault(obj => obj.TilePosition == tilePosition && !obj.IsDestroyed);

		if (objectData != null)
		{
			objectData.CurrentHealth = currentHealth;
			GD.Print($"DungeonManager: Updated object health at {tilePosition} to {currentHealth}");
		}
	}

	public override void _ExitTree()
	{
		// Clean up signal connections
		foreach (var kvp in _activeObjects)
		{
			if (kvp.Value is DungeonVase vase)
			{
				vase.DungeonVaseDestroyed -= OnDungeonObjectDestroyed;
			}
		}

		base._ExitTree();
	}
}
