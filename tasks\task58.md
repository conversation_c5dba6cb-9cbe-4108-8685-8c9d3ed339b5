Task-1:
I added Switch and DoorForSwitch. I want you to program them in following way:
1) initially door is closed and player can't pass it
2) when player is in area2d of switch, a keyE signal has to be invoked and when leaves then this signal should be to hide keye.
3) when player clicks E then you need to play animation "Open" from switch and then play animation "Open" from door. When door is opened then player can pass it (animation will handle everything)
4) when player clicks E again when in area2d of switch then you need to play animation "Close" from switch and then play animation "Close" from door. When door is closed then player can't pass it (animation will handle everything)
5) add in common signals a signal called SwitchPressed and emit it when player clicks E when in area2d of switch. It needs parameter - string. this will be name of switch that will be set in inspector. also in door inspector i will set same name so you know which switch opens which door.
6) area2d in switch - make sure it is li